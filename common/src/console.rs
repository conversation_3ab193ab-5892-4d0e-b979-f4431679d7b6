use core::fmt::{self, Write};
use spin::Mutex;

pub struct Console;

impl Write for Console {
    fn write_str(&mut self, s: &str) -> fmt::Result {
        for byte in s.bytes() {
            #[cfg(target_arch = "riscv64")]
            unsafe {
                // 使用 SBI 调用输出字符
                core::arch::asm!(
                    "ecall",
                    in("a0") byte as usize,
                    in("a6") 0,
                    in("a7") 1,
                );
            }
            
            #[cfg(target_arch = "loongarch64")]
            unsafe {
                // LoongArch64 UART 输出 (需要根据具体硬件调整)
                let uart_base = 0x1fe001e0 as *mut u8;
                uart_base.write_volatile(byte);
            }
        }
        Ok(())
    }
}

static CONSOLE: Mutex<Console> = Mutex::new(Console);

pub fn _print(args: fmt::Arguments) {
    CONSOLE.lock().write_fmt(args).unwrap();
}
