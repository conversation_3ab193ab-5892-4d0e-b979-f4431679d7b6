use core::arch::asm;

pub fn init() {
    unsafe {
        // 设置异常处理入口
        set_exception_handler(exception_handler as usize);
        
        // 启用中断
        enable_interrupts();
    }
}

pub fn enable_interrupts() {
    unsafe {
        asm!("csrxchg $r0, $r0, 0x0", options(nostack));
    }
}

pub fn disable_interrupts() {
    unsafe {
        asm!("csrxchg $r0, $r0, 0x4", options(nostack));
    }
}

unsafe fn set_exception_handler(handler: usize) {
    // 设置异常处理程序地址到 EENTRY 寄存器
    asm!("csrwr {}, 0xc", in(reg) handler, options(nostack));
}

#[no_mangle]
pub extern "C" fn exception_handler() {
    println!("Exception occurred!");
    loop {}
}
