use riscv::register::{sstatus, sie, stvec, sscratch};

pub fn init() {
    unsafe {
        // 设置中断向量表
        stvec::write(trap_handler as usize, stvec::TrapMode::Direct);
        
        // 启用中断
        sstatus::set_sie();
        sie::set_sext();
        sie::set_stimer();
        sie::set_ssoft();
    }
}

pub fn enable_interrupts() {
    unsafe {
        sstatus::set_sie();
    }
}

pub fn disable_interrupts() {
    unsafe {
        sstatus::clear_sie();
    }
}

#[no_mangle]
pub extern "C" fn trap_handler() {
    println!("Trap occurred!");
    loop {}
}
