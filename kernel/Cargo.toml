[package]
name = "kernel"
version = "0.1.0"
edition = "2021"

[dependencies]
common = { path = "../common" }
spin = { workspace = true }
lazy_static = { workspace = true }
bitflags = { workspace = true }
linked_list_allocator = { workspace = true }
volatile = { workspace = true }

# RISC-V 特定依赖
[target.'cfg(target_arch = "riscv64")'.dependencies]
riscv = "0.10"

# LoongArch64 特定依赖 (目前生态较少，主要使用内联汇编)
[target.'cfg(target_arch = "loongarch64")'.dependencies]

[lib]
crate-type = ["staticlib"]

[[bin]]
name = "kernel"
path = "src/main.rs"
