OUTPUT_ARCH(loongarch64)
ENTRY(_start)

MEMORY
{
    RAM : ORIGIN = 0x90000000, LENGTH = 128M
}

SECTIONS
{
    .text : {
        *(.text.boot)
        *(.text .text.*)
    } > RAM

    .rodata : {
        *(.rodata .rodata.*)
    } > RAM

    .data : {
        *(.data .data.*)
    } > RAM

    .bss : {
        *(.bss .bss.*)
        *(COMMON)
    } > RAM

    /DISCARD/ : {
        *(.comment)
        *(.gnu*)
        *(.note*)
        *(.eh_frame*)
    }
}
