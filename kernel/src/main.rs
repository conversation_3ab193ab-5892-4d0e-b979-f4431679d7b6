#![no_std]
#![no_main]

extern crate alloc;

use common::{println, memory, arch};

#[no_mangle]
pub extern "C" fn _start() -> ! {
    println!("Echos Kernel Starting...");
    
    // 初始化内存分配器
    memory::init_heap();
    println!("Heap initialized");
    
    // 初始化架构相关代码
    arch::init();
    println!("Architecture initialized");
    
    // 启用中断
    arch::enable_interrupts();
    println!("Interrupts enabled");
    
    println!("Kernel initialization complete!");
    
    // 主循环
    loop {
        #[cfg(target_arch = "riscv64")]
        unsafe {
            core::arch::asm!("wfi");
        }
        
        #[cfg(target_arch = "loongarch64")]
        unsafe {
            core::arch::asm!("idle 0");
        }
    }
}
