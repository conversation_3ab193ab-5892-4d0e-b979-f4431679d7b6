[build]
target-dir = "target"

[target.riscv64gc-unknown-none-elf]
rustflags = [
    "-C", "link-arg=-Tkernel/src/linker-riscv64.ld",
    "-C", "force-frame-pointers=yes",
]

[target.loongarch64-unknown-none]
rustflags = [
    "-C", "link-arg=-Tkernel/src/linker-loongarch64.ld",
    "-C", "force-frame-pointers=yes",
]

[unstable]
build-std = ["core", "compiler_builtins", "alloc"]
build-std-features = ["compiler-builtins-mem"]
