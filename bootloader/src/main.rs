#![no_std]
#![no_main]

use common::println;

#[no_mangle]
pub extern "C" fn _start() -> ! {
    println!("Echos Bootloader Starting...");
    
    // 简单的引导加载程序
    // 在实际实现中，这里会加载内核映像
    
    println!("Loading kernel...");
    
    // 跳转到内核入口点
    // 这里应该是实际的内核加载和跳转逻辑
    unsafe {
        let kernel_entry: extern "C" fn() -> ! = core::mem::transmute(0x80200000usize);
        kernel_entry();
    }
}
